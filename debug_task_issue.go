package main

import (
	"context"
	"fmt"
	"log"
	"os"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	// Initialize the application
	initializer.InitConfig()
	initializer.InitLogger()
	initializer.InitDB()

	ctx := context.Background()
	service := activity_cashback.NewActivityCashbackService()

	// Test user ID from your example
	testUserID := uuid.MustParse("eb273255-7dae-4f2e-b0e0-b23a7d13c497") // Replace with actual user ID

	fmt.Println("=== Debugging MEME Trade Task Issue ===")

	// 1. Check if daily category exists
	fmt.Println("\n1. Checking daily category...")
	dailyTasks, err := service.GetTasksByCategory(ctx, model.CategoryDaily)
	if err != nil {
		log.Printf("Error getting daily tasks: %v", err)
	} else {
		fmt.Printf("Found %d daily tasks\n", len(dailyTasks))
		for _, task := range dailyTasks {
			if task.TaskIdentifier != nil {
				fmt.Printf("  - Task: %s, ID: %s, Identifier: %s, Points: %d, Active: %t\n", 
					task.Name, task.ID.String(), *task.TaskIdentifier, task.Points, task.IsActive)
			} else {
				fmt.Printf("  - Task: %s, ID: %s, Identifier: nil, Points: %d, Active: %t\n", 
					task.Name, task.ID.String(), task.Points, task.IsActive)
			}
		}
	}

	// 2. Look specifically for MEME_TRADE_DAILY task
	fmt.Println("\n2. Looking for MEME_TRADE_DAILY task...")
	var memeTradeTask *model.ActivityTask
	for _, task := range dailyTasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDMemeTradeDaily {
			memeTradeTask = &task
			fmt.Printf("Found MEME_TRADE_DAILY task: %s (ID: %s)\n", task.Name, task.ID.String())
			break
		}
	}

	if memeTradeTask == nil {
		fmt.Println("MEME_TRADE_DAILY task not found in daily category!")
		
		// Check all tasks
		fmt.Println("\n3. Checking all tasks for MEME_TRADE_DAILY...")
		allTasks, err := service.GetAllTasks(ctx)
		if err != nil {
			log.Printf("Error getting all tasks: %v", err)
		} else {
			for _, task := range allTasks {
				if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDMemeTradeDaily {
					fmt.Printf("Found MEME_TRADE_DAILY in category %d: %s (ID: %s)\n", 
						task.CategoryID, task.Name, task.ID.String())
					memeTradeTask = &task
					break
				}
			}
		}
	}

	if memeTradeTask == nil {
		fmt.Println("MEME_TRADE_DAILY task not found anywhere! Need to seed tasks.")
		os.Exit(1)
	}

	// 3. Check user's task progress for this task
	fmt.Printf("\n4. Checking user progress for task %s...\n", memeTradeTask.ID.String())
	progress, err := service.GetTaskProgress(ctx, testUserID, memeTradeTask.ID)
	if err != nil {
		fmt.Printf("No progress found for user (this is normal for first time): %v\n", err)
	} else {
		fmt.Printf("User progress: Status=%s, Progress=%d, Target=%v, Completed=%v, Count=%d\n",
			progress.Status, progress.ProgressValue, 
			progress.TargetValue, progress.LastCompletedAt, progress.CompletionCount)
	}

	// 4. Check user's tier info
	fmt.Printf("\n5. Checking user tier info...\n")
	tierInfo, err := service.GetUserTierInfo(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error getting tier info: %v\n", err)
	} else {
		fmt.Printf("User tier: Level=%d, Points=%d, Monthly=%d\n", 
			tierInfo.CurrentTier, tierInfo.TotalPoints, tierInfo.PointsThisMonth)
	}

	// 5. Test task processing manually
	fmt.Printf("\n6. Testing task processing manually...\n")
	tradeData := map[string]interface{}{
		"trade_type": "MEME",
		"volume":     100.0,
		"order_id":   "test-order-123",
		"user_id":    testUserID.String(),
	}

	// Create task processor manager
	taskManager := activity_cashback.NewTaskProcessorManager(service)
	
	fmt.Printf("Processing MEME trade task for user %s...\n", testUserID.String())
	err = taskManager.ProcessTaskByIdentifier(ctx, testUserID, model.TaskIDMemeTradeDaily, model.CategoryDaily, tradeData)
	if err != nil {
		fmt.Printf("Error processing task: %v\n", err)
	} else {
		fmt.Println("Task processed successfully!")
	}

	// 6. Check progress again after processing
	fmt.Printf("\n7. Checking progress after processing...\n")
	progress, err = service.GetTaskProgress(ctx, testUserID, memeTradeTask.ID)
	if err != nil {
		fmt.Printf("Error getting progress: %v\n", err)
	} else {
		fmt.Printf("Updated progress: Status=%s, Progress=%d, Target=%v, Completed=%v, Count=%d, Points=%d\n",
			progress.Status, progress.ProgressValue, 
			progress.TargetValue, progress.LastCompletedAt, progress.CompletionCount, progress.PointsEarned)
	}

	// 7. Check tier info again
	fmt.Printf("\n8. Checking tier info after processing...\n")
	tierInfo, err = service.GetUserTierInfo(ctx, testUserID)
	if err != nil {
		fmt.Printf("Error getting tier info: %v\n", err)
	} else {
		fmt.Printf("Updated tier: Level=%d, Points=%d, Monthly=%d\n", 
			tierInfo.CurrentTier, tierInfo.TotalPoints, tierInfo.PointsThisMonth)
	}

	fmt.Println("\n=== Debug Complete ===")
}
