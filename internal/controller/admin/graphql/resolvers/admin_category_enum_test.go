package resolvers

import (
	"testing"

	"github.com/stretchr/testify/assert"
	admin_gql_model "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func TestAdminCategoryEnumConversion(t *testing.T) {
	t.Run("convertModelCategoryToAdminGQL converts correctly", func(t *testing.T) {
		assert.Equal(t, admin_gql_model.TaskCategoryNameDaily, convertModelCategoryToAdminGQL(model.CategoryDaily))
		assert.Equal(t, admin_gql_model.TaskCategoryNameCommunity, convertModelCategoryToAdminGQL(model.CategoryCommunity))
		assert.Equal(t, admin_gql_model.TaskCategoryNameTrading, convertModelCategoryToAdminGQL(model.CategoryTrading))
		
		// Test default case
		assert.Equal(t, admin_gql_model.TaskCategoryNameDaily, convertModelCategoryToAdminGQL(model.TaskCategoryName("invalid")))
	})

	t.Run("convertAdminGQLCategoryToModel converts correctly", func(t *testing.T) {
		assert.Equal(t, model.CategoryDaily, convertAdminGQLCategoryToModel(admin_gql_model.TaskCategoryNameDaily))
		assert.Equal(t, model.CategoryCommunity, convertAdminGQLCategoryToModel(admin_gql_model.TaskCategoryNameCommunity))
		assert.Equal(t, model.CategoryTrading, convertAdminGQLCategoryToModel(admin_gql_model.TaskCategoryNameTrading))
		
		// Test default case
		assert.Equal(t, model.CategoryDaily, convertAdminGQLCategoryToModel(admin_gql_model.TaskCategoryName("INVALID")))
	})

	t.Run("round trip conversion maintains consistency", func(t *testing.T) {
		modelCategories := []model.TaskCategoryName{
			model.CategoryDaily,
			model.CategoryCommunity,
			model.CategoryTrading,
		}

		for _, modelCategory := range modelCategories {
			adminGQLCategory := convertModelCategoryToAdminGQL(modelCategory)
			backToModel := convertAdminGQLCategoryToModel(adminGQLCategory)
			assert.Equal(t, modelCategory, backToModel, "Round trip conversion failed for %s", modelCategory)
		}
	})

	t.Run("admin GraphQL enum values are correct", func(t *testing.T) {
		assert.Equal(t, "DAILY", string(admin_gql_model.TaskCategoryNameDaily))
		assert.Equal(t, "COMMUNITY", string(admin_gql_model.TaskCategoryNameCommunity))
		assert.Equal(t, "TRADING", string(admin_gql_model.TaskCategoryNameTrading))
	})

	t.Run("admin GraphQL enum validation works", func(t *testing.T) {
		assert.True(t, admin_gql_model.TaskCategoryNameDaily.IsValid())
		assert.True(t, admin_gql_model.TaskCategoryNameCommunity.IsValid())
		assert.True(t, admin_gql_model.TaskCategoryNameTrading.IsValid())
		assert.False(t, admin_gql_model.TaskCategoryName("INVALID").IsValid())
	})
}
